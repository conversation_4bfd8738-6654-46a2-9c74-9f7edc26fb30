#!/usr/bin/env python3
"""
Simple Voice Connection Test for Luna Discord Bot
Run this in your luna-discord conda environment
"""

import asyncio
import logging
import sys

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(message)s')
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "MTMyOTYxMTUxMjc3OTkwMzA2Ng.GGnJA3.yGEODHLRZi6tTcLvPikDR4Akd0nW9vjIHbiD2M"
TARGET_VOICE_CHANNEL_ID = 509585784639193093

async def test_voice_connection():
    """Simple voice connection test"""
    
    # Check imports first
    try:
        import discord
        from discord.ext import commands
        logger.info(f"✅ py-cord version: {discord.__version__}")
    except ImportError as e:
        logger.error(f"❌ Failed to import discord: {e}")
        logger.error("💡 Make sure you're in the luna-discord conda environment")
        logger.error("💡 Install with: pip install py-cord[voice]")
        return False
    
    # Check voice dependencies
    try:
        import nacl
        logger.info(f"✅ PyNaCl version: {nacl.__version__}")
    except ImportError:
        logger.error("❌ PyNaCl not found - voice won't work")
        logger.error("💡 Install with: pip install PyNaCl")
        return False
    
    # Set up bot
    intents = discord.Intents.default()
    intents.voice_states = True
    intents.guilds = True
    
    bot = commands.Bot(command_prefix='!', intents=intents)
    
    @bot.event
    async def on_ready():
        logger.info(f"🤖 Bot logged in as {bot.user.name}")
        
        # Find the voice channel
        voice_channel = bot.get_channel(TARGET_VOICE_CHANNEL_ID)
        if not voice_channel:
            logger.error(f"❌ Could not find voice channel with ID: {TARGET_VOICE_CHANNEL_ID}")
            await bot.close()
            return
        
        logger.info(f"🎯 Found voice channel: {voice_channel.name} in {voice_channel.guild.name}")
        
        # Test connection
        try:
            logger.info("🔗 Attempting voice connection...")
            vc = await voice_channel.connect()
            
            if vc and vc.is_connected():
                logger.info("✅ SUCCESS! Voice connection established!")
                logger.info(f"🔗 Connected to: {voice_channel.name}")
                
                # Stay connected for 10 seconds
                logger.info("⏳ Staying connected for 10 seconds...")
                await asyncio.sleep(10)
                
                # Disconnect
                await vc.disconnect()
                logger.info("🔌 Disconnected successfully")
                
            else:
                logger.error("❌ Connection failed - not verified as connected")
                
        except discord.errors.ConnectionClosed as e:
            logger.error(f"❌ Connection closed with code: {e.code}")
            if e.code == 4006:
                logger.error("💡 This is the known 4006 voice handshake issue!")
                logger.error("💡 Discord changed their voice infrastructure")
            
        except Exception as e:
            logger.error(f"❌ Connection failed: {e}")
            logger.error(f"📋 Error type: {type(e).__name__}")
        
        finally:
            await bot.close()
    
    # Start the bot
    try:
        await bot.start(BOT_TOKEN)
    except Exception as e:
        logger.error(f"❌ Bot failed to start: {e}")
        return False
    
    return True

def main():
    """Main function"""
    logger.info("🚀 Simple Voice Connection Test")
    logger.info("=" * 50)
    logger.info(f"🎯 Target Voice Channel ID: {TARGET_VOICE_CHANNEL_ID}")
    logger.info("🔍 This will test basic voice connection functionality")
    logger.info("")
    
    try:
        # Run the test
        result = asyncio.run(test_voice_connection())
        
        if result:
            logger.info("🎉 Test completed successfully!")
        else:
            logger.error("😞 Test failed - check errors above")
            
    except KeyboardInterrupt:
        logger.info("🛑 Test stopped by user")
    except Exception as e:
        logger.error(f"💥 Test crashed: {e}")
        import traceback
        logger.error(f"📋 Traceback:\n{traceback.format_exc()}")

if __name__ == "__main__":
    main()
