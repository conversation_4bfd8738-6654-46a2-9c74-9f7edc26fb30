#!/usr/bin/env python3
"""
Voice Connection Test Script for py-cord 4006 Error Debugging
This script will test various voice connection methods until one works.

USAGE:
1. Run this script: python voice_connection_test.py
2. The bot will automatically test different connection methods
3. Check the console output and voice_test.log for results
4. If a method works, it will be highlighted in the summary

TROUBLESHOOTING:
- Make sure the bot has permission to join the voice channel
- Ensure the voice channel ID is correct
- Check that py-cord[voice] is properly installed
"""

import discord
from discord.ext import commands
import asyncio
import logging
import time
import sys
import traceback
import subprocess
import pkg_resources

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('voice_test.log')
    ]
)
logger = logging.getLogger(__name__)

# Configuration
BOT_TOKEN = "ec2690b41ff13cdc45f080c35d8450f74cd07c321eaf9d8c235d49e939207eaa"
TARGET_VOICE_CHANNEL_ID = 509585784639193093

# Test counter
test_attempt = 0
successful_connections = 0

def check_py_cord_version():
    """Check the current py-cord version and voice dependencies"""
    logger.info("🔍 Checking py-cord installation...")

    try:
        # Check py-cord version
        try:
            discord_version = pkg_resources.get_distribution("py-cord").version
            logger.info(f"📦 py-cord version: {discord_version}")
        except pkg_resources.DistributionNotFound:
            logger.error("❌ py-cord not found! Please install with: pip install py-cord[voice]")
            return False

        # Check voice dependencies
        voice_deps = ["PyNaCl", "aiodns", "brotlipy"]
        missing_deps = []

        for dep in voice_deps:
            try:
                version = pkg_resources.get_distribution(dep).version
                logger.info(f"📦 {dep} version: {version}")
            except pkg_resources.DistributionNotFound:
                missing_deps.append(dep)
                logger.warning(f"⚠️ {dep} not found")

        if missing_deps:
            logger.warning(f"⚠️ Missing voice dependencies: {', '.join(missing_deps)}")
            logger.info("💡 Install with: pip install PyNaCl aiodns brotlipy")

        # Check if voice is available
        if hasattr(discord, 'VoiceClient'):
            logger.info("✅ Discord voice client available")
        else:
            logger.error("❌ Discord voice client not available")
            return False

        return True

    except Exception as e:
        logger.error(f"❌ Error checking py-cord installation: {e}")
        return False

class VoiceTestBot:
    def __init__(self):
        # Set up intents
        intents = discord.Intents.default()
        intents.message_content = True
        intents.voice_states = True
        intents.guilds = True
        
        self.bot = commands.Bot(command_prefix='!test', intents=intents)
        self.setup_events()
        
    def setup_events(self):
        @self.bot.event
        async def on_ready():
            logger.info(f'🤖 Bot logged in as {self.bot.user.name} ({self.bot.user.id})')
            logger.info(f'🔍 Looking for voice channel ID: {TARGET_VOICE_CHANNEL_ID}')

            # Check py-cord installation first
            if not check_py_cord_version():
                logger.error("❌ py-cord installation issues detected. Please fix before continuing.")
                await self.bot.close()
                return

            # Start the voice connection tests
            await self.run_voice_tests()
        
        @self.bot.event
        async def on_voice_state_update(member, before, after):
            if member == self.bot.user:
                if after.channel:
                    logger.info(f"✅ Bot joined voice channel: {after.channel.name} (ID: {after.channel.id})")
                elif before.channel:
                    logger.info(f"❌ Bot left voice channel: {before.channel.name} (ID: {before.channel.id})")

        @self.bot.command(name='test_voice')
        async def manual_voice_test(ctx):
            """Manual command to test voice connection"""
            if ctx.author.voice and ctx.author.voice.channel:
                voice_channel = ctx.author.voice.channel
                await ctx.send(f"🧪 Testing voice connection to {voice_channel.name}...")

                try:
                    success = await self.test_basic_connection(voice_channel)
                    if success:
                        await ctx.send("✅ Voice connection test successful!")
                    else:
                        await ctx.send("❌ Voice connection test failed!")
                except Exception as e:
                    await ctx.send(f"💥 Voice connection test crashed: {e}")
            else:
                await ctx.send("❌ You need to be in a voice channel to test!")

        @self.bot.command(name='force_connect')
        async def force_connect(ctx):
            """Force connection to the target voice channel"""
            voice_channel = self.bot.get_channel(TARGET_VOICE_CHANNEL_ID)
            if voice_channel:
                await ctx.send(f"🔗 Force connecting to {voice_channel.name}...")
                try:
                    success = await self.test_connection_with_retry(voice_channel)
                    if success:
                        await ctx.send("✅ Force connection successful!")
                    else:
                        await ctx.send("❌ Force connection failed!")
                except Exception as e:
                    await ctx.send(f"💥 Force connection crashed: {e}")
            else:
                await ctx.send(f"❌ Could not find voice channel with ID: {TARGET_VOICE_CHANNEL_ID}")

        @self.bot.command(name='disconnect')
        async def disconnect_voice(ctx):
            """Disconnect from voice channel"""
            if ctx.guild.voice_client:
                await ctx.guild.voice_client.disconnect()
                await ctx.send("🔌 Disconnected from voice channel")
            else:
                await ctx.send("❌ Not connected to any voice channel")

    async def run_voice_tests(self):
        """Run various voice connection test methods"""
        global test_attempt, successful_connections
        
        # Find the target voice channel
        voice_channel = self.bot.get_channel(TARGET_VOICE_CHANNEL_ID)
        if not voice_channel:
            logger.error(f"❌ Could not find voice channel with ID: {TARGET_VOICE_CHANNEL_ID}")
            return
            
        logger.info(f"🎯 Found target voice channel: {voice_channel.name} in guild: {voice_channel.guild.name}")
        
        # Test methods to try
        test_methods = [
            self.test_basic_connection,
            self.test_connection_with_retry,
            self.test_connection_with_longer_timeout,
            self.test_connection_with_reconnect,
            self.test_connection_with_delay,
        ]
        
        for method in test_methods:
            test_attempt += 1
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 TEST ATTEMPT #{test_attempt}: {method.__name__}")
            logger.info(f"{'='*60}")
            
            try:
                success = await method(voice_channel)
                if success:
                    successful_connections += 1
                    logger.info(f"✅ SUCCESS! Method '{method.__name__}' worked!")
                    
                    # Wait a bit then disconnect for next test
                    await asyncio.sleep(5)
                    if voice_channel.guild.voice_client:
                        await voice_channel.guild.voice_client.disconnect()
                        logger.info("🔌 Disconnected for next test")
                        await asyncio.sleep(2)
                else:
                    logger.error(f"❌ Method '{method.__name__}' failed")
                    
            except Exception as e:
                logger.error(f"💥 Method '{method.__name__}' crashed: {e}")
                logger.error(f"📋 Traceback:\n{traceback.format_exc()}")
                
                # Clean up any partial connections
                try:
                    if voice_channel.guild.voice_client:
                        await voice_channel.guild.voice_client.disconnect()
                except:
                    pass
            
            # Wait between tests
            await asyncio.sleep(3)
        
        # Summary
        logger.info(f"\n{'='*60}")
        logger.info(f"🏁 TEST SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Total tests run: {test_attempt}")
        logger.info(f"Successful connections: {successful_connections}")
        logger.info(f"Success rate: {(successful_connections/test_attempt)*100:.1f}%")
        
        if successful_connections > 0:
            logger.info("🎉 At least one method worked! Check the logs above for details.")
        else:
            logger.error("😞 No methods worked. This confirms the 4006 voice handshake issue.")
            
        # Keep the bot running for manual testing
        logger.info("🔄 Bot will stay online for manual testing. Use Ctrl+C to exit.")

    async def test_basic_connection(self, voice_channel):
        """Test 1: Basic connection attempt"""
        logger.info("📝 Testing basic voice connection...")
        logger.info(f"🎯 Target: {voice_channel.name} in {voice_channel.guild.name}")
        logger.info(f"🌐 Voice region: {voice_channel.guild.region if hasattr(voice_channel.guild, 'region') else 'Unknown'}")

        try:
            start_time = time.time()
            vc = await voice_channel.connect()
            connect_time = time.time() - start_time

            logger.info(f"⏱️ Connection attempt took {connect_time:.2f} seconds")

            if vc and vc.is_connected():
                logger.info("✅ Basic connection successful!")
                logger.info(f"🔗 Voice client endpoint: {getattr(vc, 'endpoint', 'Unknown')}")
                return True
            else:
                logger.warning("⚠️ Connection returned but not verified as connected")
                if vc:
                    logger.warning(f"🔗 Voice client state: connected={vc.is_connected()}")
                return False
        except discord.errors.ConnectionClosed as e:
            logger.error(f"❌ Connection closed error: Code {e.code}")
            if e.code == 4006:
                logger.error("💡 This is the known 4006 voice handshake issue!")
            return False
        except discord.ClientException as e:
            logger.error(f"❌ Discord client error: {e}")
            return False
        except asyncio.TimeoutError:
            logger.error("❌ Connection timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error: {e}")
            logger.error(f"📋 Error type: {type(e).__name__}")
            return False

    async def test_connection_with_retry(self, voice_channel):
        """Test 2: Connection with retry logic for 4006 errors"""
        logger.info("📝 Testing connection with 4006 retry logic...")
        
        max_retries = 3
        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 Retry attempt {attempt + 1}/{max_retries}")
                vc = await voice_channel.connect()
                
                if vc and vc.is_connected():
                    logger.info(f"✅ Retry connection successful on attempt {attempt + 1}!")
                    return True
                    
            except discord.errors.ConnectionClosed as e:
                if e.code == 4006:
                    logger.warning(f"⚠️ 4006 error on attempt {attempt + 1}, retrying...")
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                    continue
                else:
                    logger.error(f"❌ Non-4006 connection error: {e}")
                    return False
            except Exception as e:
                logger.error(f"❌ Retry attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    return False
                await asyncio.sleep(2 ** attempt)
        
        return False

    async def test_connection_with_longer_timeout(self, voice_channel):
        """Test 3: Connection with longer timeout"""
        logger.info("📝 Testing connection with 30-second timeout...")
        
        try:
            vc = await asyncio.wait_for(voice_channel.connect(), timeout=30.0)
            if vc and vc.is_connected():
                logger.info("✅ Long timeout connection successful!")
                return True
            else:
                logger.warning("⚠️ Long timeout connection returned but not verified")
                return False
        except asyncio.TimeoutError:
            logger.error("❌ Connection timed out after 30 seconds")
            return False
        except Exception as e:
            logger.error(f"❌ Long timeout connection failed: {e}")
            return False

    async def test_connection_with_reconnect(self, voice_channel):
        """Test 4: Connection with immediate reconnect on failure"""
        logger.info("📝 Testing connection with immediate reconnect...")
        
        try:
            # First attempt
            vc = await voice_channel.connect()
            if vc and vc.is_connected():
                logger.info("✅ First reconnect attempt successful!")
                return True
            
            # If first fails, disconnect and try again immediately
            logger.info("🔄 First attempt failed, trying immediate reconnect...")
            if vc:
                await vc.disconnect()
            await asyncio.sleep(1)
            
            vc = await voice_channel.connect()
            if vc and vc.is_connected():
                logger.info("✅ Immediate reconnect successful!")
                return True
            else:
                logger.warning("⚠️ Immediate reconnect failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Reconnect test failed: {e}")
            return False

    async def test_connection_with_delay(self, voice_channel):
        """Test 5: Connection with pre-connection delay"""
        logger.info("📝 Testing connection with 5-second pre-delay...")
        
        try:
            logger.info("⏳ Waiting 5 seconds before connection attempt...")
            await asyncio.sleep(5)
            
            vc = await voice_channel.connect()
            if vc and vc.is_connected():
                logger.info("✅ Delayed connection successful!")
                return True
            else:
                logger.warning("⚠️ Delayed connection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Delayed connection failed: {e}")
            return False

    async def run(self):
        """Start the bot"""
        try:
            await self.bot.start(BOT_TOKEN)
        except KeyboardInterrupt:
            logger.info("🛑 Bot stopped by user")
        except Exception as e:
            logger.error(f"💥 Bot crashed: {e}")
            logger.error(f"📋 Traceback:\n{traceback.format_exc()}")
        finally:
            await self.bot.close()

async def main():
    """Main function"""
    logger.info("🚀 Starting Voice Connection Test Bot...")
    logger.info(f"🎯 Target Voice Channel ID: {TARGET_VOICE_CHANNEL_ID}")
    logger.info("📝 This bot will test various voice connection methods")
    logger.info("🔍 Check voice_test.log for detailed logs")
    
    bot = VoiceTestBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("🛑 Test script stopped by user")
    except Exception as e:
        logger.error(f"💥 Test script crashed: {e}")
        logger.error(f"📋 Traceback:\n{traceback.format_exc()}")
